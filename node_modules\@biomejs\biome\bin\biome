#!/usr/bin/env node
const { platform, arch, env, version, release } = process;
const { execSync } = require("child_process");

function isMusl() {
	let stderr;
	try {
		stderr = execSync("ldd --version", {
			stdio: ['pipe', 'pipe', 'pipe']
		});
	} catch (err) {
		stderr = err.stderr;
	}
	if (stderr.indexOf("musl") > -1) {
		return true;
	}
	return false;
}

const PLATFORMS = {
	win32: {
		x64: "@biomejs/cli-win32-x64/biome.exe",
		arm64: "@biomejs/cli-win32-arm64/biome.exe",
	},
	darwin: {
		x64: "@biomejs/cli-darwin-x64/biome",
		arm64: "@biomejs/cli-darwin-arm64/biome",
	},
	linux: {
		x64: "@biomejs/cli-linux-x64/biome",
		arm64: "@biomejs/cli-linux-arm64/biome",
	},
	"linux-musl": {
		x64: "@biomejs/cli-linux-x64-musl/biome",
		arm64: "@biomejs/cli-linux-arm64-musl/biome",
	},
};
if (env.ROME_BINARY) {
	console.warn(`[WARN] The environment variable "ROME_BINARY" is deprecated. Use "BIOME_BINARY" instead.`)
}

const binPath = env.BIOME_BINARY || env.ROME_BINARY ||
	(platform === "linux" && isMusl()
		? PLATFORMS?.["linux-musl"]?.[arch]
		: PLATFORMS?.[platform]?.[arch]
	);

if (binPath) {
	const packageManager = detectPackageManager();
	const result = require("child_process").spawnSync(
		require.resolve(binPath),
		process.argv.slice(2),
		{
			shell: false,
			stdio: "inherit",
			env: {
				...env,
				JS_RUNTIME_VERSION: version,
				JS_RUNTIME_NAME: release.name,
				...(packageManager != null
					? { NODE_PACKAGE_MANAGER: packageManager }
					: {}),
			},
		},
	);

	if (result.error) {
		throw result.error;
	}

	process.exitCode = result.status;
} else {
	console.error(
		"The Biome CLI package doesn't ship with prebuilt binaries for your platform yet. " +
			"You can still use the CLI by cloning the biome/tools repo from GitHub, " +
			"and follow the instructions there to build the CLI for your platform.",
	);
	process.exitCode = 1;
}

/**
 * NPM, Yarn, and other package manager set the `npm_config_user_agent`. It has the following format:
 *
 * ```
 * "npm/8.3.0 node/v16.13.2 win32 x64 workspaces/false
 * ```
 *
 * @returns The package manager string (`npm/8.3.0`) or null if the user agent string isn't set.
 */
function detectPackageManager() {
	const userAgent = env.npm_config_user_agent;

	if (userAgent == null) {
		return null;
	}

	return userAgent.split(" ")[0];
}

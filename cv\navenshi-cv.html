<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON>idoo - Frontend Developer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header .title {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .contact-info a {
            color: white;
            text-decoration: none;
            opacity: 0.9;
            transition: opacity 0.3s;
        }
        
        .contact-info a:hover {
            opacity: 1;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #667eea;
            font-size: 1.4em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .summary {
            font-size: 1.1em;
            line-height: 1.7;
            color: #555;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .skill-category h3 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .skill-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .skill-tag {
            background: #e9ecef;
            color: #495057;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .experience-item, .project-item {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .experience-item:last-child, .project-item:last-child {
            border-bottom: none;
        }
        
        .job-header, .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }
        
        .job-title, .project-title {
            font-weight: 600;
            color: #495057;
            font-size: 1.1em;
        }
        
        .company, .project-tech {
            color: #667eea;
            font-weight: 500;
        }
        
        .date {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .achievements {
            list-style: none;
            margin-top: 10px;
        }
        
        .achievements li {
            position: relative;
            padding-left: 20px;
            margin-bottom: 8px;
            color: #555;
        }
        
        .achievements li:before {
            content: "▸";
            position: absolute;
            left: 0;
            color: #667eea;
            font-weight: bold;
        }
        
        .languages {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .language {
            text-align: center;
        }
        
        .language-name {
            font-weight: 600;
            color: #495057;
        }
        
        .language-level {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .contact-info {
                gap: 15px;
            }
            
            .job-header, .project-header {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Navenshia Naidoo</h1>
            <div class="title">Frontend Developer</div>
            <div class="contact-info">
                <a href="mailto:<EMAIL>">📧 <EMAIL></a>
                <a href="https://linkedin.com/in/navenshia-naidoo" target="_blank">💼 LinkedIn</a>
                <a href="https://github.com/navenshia-naidoo" target="_blank">💻 GitHub</a>
            </div>
        </header>
        
        <div class="content">
            <section class="section">
                <h2>Summary</h2>
                <div class="summary">
                    Passionate 22-year-old South African Frontend Developer with expertise in React, TypeScript, and modern web technologies.
                    Native English speaker with multilingual abilities (Afrikaans, Spanish, Japanese) and strong problem-solving skills.
                    Experienced in building responsive, accessible web applications and seeking opportunities to contribute to innovative projects.
                </div>
            </section>
            
            <section class="section">
                <h2>Technical Skills</h2>
                <div class="skills-grid">
                    <div class="skill-category">
                        <h3>Frontend</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">React</span>
                            <span class="skill-tag">Vue.js</span>
                            <span class="skill-tag">TypeScript</span>
                            <span class="skill-tag">JavaScript</span>
                            <span class="skill-tag">HTML5</span>
                            <span class="skill-tag">CSS3</span>
                        </div>
                    </div>
                    <div class="skill-category">
                        <h3>Tools & Others</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">Git</span>
                            <span class="skill-tag">Vite</span>
                            <span class="skill-tag">npm</span>
                            <span class="skill-tag">REST APIs</span>
                            <span class="skill-tag">Responsive Design</span>
                            <span class="skill-tag">Accessibility</span>
                        </div>
                    </div>
                </div>
            </section>
            
            <section class="section">
                <h2>Projects</h2>
                <div class="project-item">
                    <div class="project-header">
                        <div>
                            <div class="project-title">Password Validation Challenge</div>
                            <div class="project-tech">React • TypeScript • API Integration</div>
                        </div>
                        <div class="date">2025</div>
                    </div>
                    <ul class="achievements">
                        <li>Built comprehensive form validation with real-time password criteria checking</li>
                        <li>Implemented secure API integration with JWT authentication</li>
                        <li>Added full accessibility support with ARIA attributes and screen reader compatibility</li>
                        <li>Handled multiple error scenarios with user-friendly messaging</li>
                    </ul>
                </div>
                
                <!-- Add more projects here -->
                <div class="project-item">
                    <div class="project-header">
                        <div>
                            <div class="project-title">Portfolio Website</div>
                            <div class="project-tech">Vue.js • CSS3 • Responsive Design</div>
                        </div>
                        <div class="date">2024</div>
                    </div>
                    <ul class="achievements">
                        <li>Designed and developed responsive portfolio showcasing technical projects</li>
                        <li>Implemented smooth animations and modern UI/UX principles</li>
                        <li>Optimized for performance and cross-browser compatibility</li>
                    </ul>
                </div>
            </section>
            
            <section class="section">
                <h2>Languages</h2>
                <div class="languages">
                    <div class="language">
                        <div class="language-name">English</div>
                        <div class="language-level">Native</div>
                    </div>
                    <div class="language">
                        <div class="language-name">Afrikaans</div>
                        <div class="language-level">Fluent</div>
                    </div>
                    <div class="language">
                        <div class="language-name">Spanish</div>
                        <div class="language-level">Conversational</div>
                    </div>
                    <div class="language">
                        <div class="language-name">Japanese</div>
                        <div class="language-level">Basic (Learning)</div>
                    </div>
                </div>
            </section>
            
            <section class="section">
                <h2>Education</h2>
                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <div class="job-title">Computer Science / Web Development</div>
                            <div class="company">Self-Taught & Online Courses</div>
                        </div>
                        <div class="date">2023 - Present</div>
                    </div>
                    <ul class="achievements">
                        <li>Completed comprehensive courses in React, TypeScript, and modern web development</li>
                        <li>Built multiple projects demonstrating frontend development skills</li>
                        <li>Continuously learning new technologies and best practices</li>
                    </ul>
                </div>
            </section>
        </div>
    </div>
</body>
</html>

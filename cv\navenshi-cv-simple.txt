NAVENSHIA NAIDOO
Frontend Developer

Contact: <EMAIL> | LinkedIn: linkedin.com/in/navenshia-naidoo | GitHub: github.com/navenshia-naidoo

SUMMARY
=======
Passionate 22-year-old South African Frontend Developer with expertise in React, TypeScript, and modern
web technologies. Native English speaker with multilingual abilities (Afrikaans, Spanish, Japanese) and
strong problem-solving skills. Experienced in building responsive, accessible web applications and seeking
opportunities to contribute to innovative projects.

TECHNICAL SKILLS
===============
Frontend: React, Vue.js, TypeScript, JavaScript, HTML5, CSS3
Tools: Git, Vite, npm, REST APIs, Responsive Design, Accessibility (ARIA)

PROJECTS
========
Password Validation Challenge (React, TypeScript, API Integration) - 2025
• Built comprehensive form validation with real-time password criteria checking
• Implemented secure API integration with JWT authentication  
• Added full accessibility support with ARIA attributes and screen reader compatibility
• Handled multiple error scenarios with user-friendly messaging

Portfolio Website (Vue.js, CSS3, Responsive Design) - 2024
• Designed and developed responsive portfolio showcasing technical projects
• Implemented smooth animations and modern UI/UX principles
• Optimized for performance and cross-browser compatibility

EXPERIENCE
==========
Frontend Developer (Personal Projects) - 2023 - Present
• Developed multiple web applications using React and Vue.js
• Implemented responsive designs with focus on user experience
• Integrated with various APIs and handled authentication flows
• Applied accessibility best practices and modern development workflows

LANGUAGES
=========
English: Native (Home Language)
Afrikaans: Fluent
Spanish: Conversational
Japanese: Basic (Learning - conversational level)

EDUCATION
=========
Computer Science / Web Development (Self-Taught & Online Courses) - 2023 - Present
• Completed comprehensive courses in React, TypeScript, and modern web development
• Built multiple projects demonstrating frontend development skills
• Continuously learning new technologies and best practices

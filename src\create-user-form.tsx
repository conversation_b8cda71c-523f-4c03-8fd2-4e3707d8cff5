import type { CSSProperties, Dispatch, SetStateAction } from 'react';
import { useCallback, useState } from 'react';

interface CreateUserFormProps {
  setUserWasCreated: Dispatch<SetStateAction<boolean>>;
}

// Password validation criteria
const validatePassword = (password: string): string[] => {
  const errors: string[] = [];

  if (password.length < 10) {
    errors.push('Password must be at least 10 characters long');
  }

  if (password.length > 24) {
    errors.push('Password must be at most 24 characters long');
  }

  if (password.includes(' ')) {
    errors.push('Password cannot contain spaces');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  return errors;
};

function CreateUserForm({ setUserWasCreated }: CreateUserFormProps) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [passwordErrors, setPasswordErrors] = useState<string[]>([]);
  const [apiError, setApiError] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handlePasswordChange = useCallback(
    (value: string) => {
      setPassword(value);
      setPasswordErrors(validatePassword(value));
      // Clear API errors when user starts typing
      if (apiError) {
        setApiError('');
      }
    },
    [apiError],
  );

  const handleUsernameChange = useCallback(
    (value: string) => {
      setUsername(value);
      // Clear API errors when user starts typing
      if (apiError) {
        setApiError('');
      }
    },
    [apiError],
  );

  const isFormValid = username.trim() !== '' && passwordErrors.length === 0;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isFormValid || isSubmitting) {
      return;
    }

    setIsSubmitting(true);
    setApiError('');

    try {
      // Authentication token from the HENNGE challenge details page
      const authToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************.HHrJIA0gxWRQ3iaaRg64IdyssbbbcKdFxWCPCCrjGBg';

      const response = await fetch(
        'https://api.challenge.hennge.com/password-validation-challenge-api/001/challenge-signup',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${authToken}`,
          },
          body: JSON.stringify({
            username: username.trim(),
            password: password,
          }),
        },
      );

      if (response.ok) {
        try {
          const data = await response.json();
          if (data.success) {
            setUserWasCreated(true);
          } else {
            setApiError('Something went wrong, please try again.');
          }
        } catch (parseError) {
          setApiError('Something went wrong, please try again.');
        }
      } else if (response.status === 401 || response.status === 403) {
        setApiError('Not authenticated to access this resource.');
      } else if (response.status === 422) {
        try {
          const data = await response.json();
          if (
            data.errors &&
            Array.isArray(data.errors) &&
            data.errors.includes('not_allowed')
          ) {
            setApiError(
              'Sorry, the entered password is not allowed, please try a different one.',
            );
          } else {
            setApiError('Something went wrong, please try again.');
          }
        } catch (parseError) {
          setApiError('Something went wrong, please try again.');
        }
      } else if (response.status === 500) {
        setApiError('Something went wrong, please try again.');
      } else {
        setApiError('Something went wrong, please try again.');
      }
    } catch (error) {
      setApiError('Something went wrong, please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div style={formWrapper}>
      <form style={form} onSubmit={handleSubmit}>
        <label style={formLabel} htmlFor="username">
          Username
        </label>
        <input
          id="username"
          style={formInput}
          value={username}
          onChange={(e) => handleUsernameChange(e.target.value)}
          aria-label="Username"
        />

        <label style={formLabel} htmlFor="password">
          Password
        </label>
        <input
          id="password"
          type="password"
          style={formInput}
          value={password}
          onChange={(e) => handlePasswordChange(e.target.value)}
          aria-label="Password"
          aria-describedby={
            passwordErrors.length > 0 ? 'password-errors' : undefined
          }
          aria-invalid={passwordErrors.length > 0}
        />

        {/* Password validation errors */}
        {passwordErrors.length > 0 && (
          <div
            id="password-errors"
            style={errorContainer}
            role="alert"
            aria-live="polite"
          >
            <ul style={errorList}>
              {passwordErrors.map((error) => (
                <li key={error} style={errorItem}>
                  {error}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* API error messages */}
        {apiError && (
          <div style={apiErrorContainer} role="alert" aria-live="assertive">
            {apiError}
          </div>
        )}

        <button
          type="submit"
          style={{
            ...formButton,
            opacity: isFormValid && !isSubmitting ? 1 : 0.6,
            cursor: isFormValid && !isSubmitting ? 'pointer' : 'not-allowed',
          }}
          disabled={!isFormValid || isSubmitting}
        >
          {isSubmitting ? 'Creating...' : 'Create User'}
        </button>
      </form>
    </div>
  );
}

export { CreateUserForm };

const formWrapper: CSSProperties = {
  maxWidth: '500px',
  width: '80%',
  backgroundColor: '#efeef5',
  padding: '24px',
  borderRadius: '8px',
};

const form: CSSProperties = {
  display: 'flex',
  flexDirection: 'column',
  gap: '8px',
};

const formLabel: CSSProperties = {
  fontWeight: 700,
};

const formInput: CSSProperties = {
  outline: 'none',
  padding: '8px 16px',
  height: '40px',
  fontSize: '14px',
  backgroundColor: '#f8f7fa',
  border: '1px solid rgba(0, 0, 0, 0.12)',
  borderRadius: '4px',
};

const formButton: CSSProperties = {
  outline: 'none',
  borderRadius: '4px',
  border: '1px solid rgba(0, 0, 0, 0.12)',
  backgroundColor: '#7135d2',
  color: 'white',
  fontSize: '16px',
  fontWeight: 500,
  height: '40px',
  padding: '0 8px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginTop: '8px',
  alignSelf: 'flex-end',
  cursor: 'pointer',
};

const errorContainer: CSSProperties = {
  marginTop: '4px',
};

const errorList: CSSProperties = {
  margin: 0,
  paddingLeft: '20px',
  color: '#d32f2f',
  fontSize: '14px',
};

const errorItem: CSSProperties = {
  marginBottom: '4px',
};

const apiErrorContainer: CSSProperties = {
  marginTop: '8px',
  padding: '8px 12px',
  backgroundColor: '#ffebee',
  border: '1px solid #f44336',
  borderRadius: '4px',
  color: '#d32f2f',
  fontSize: '14px',
};

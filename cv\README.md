# Navenshia Naidoo
**Frontend Developer**

📧 <EMAIL> | 💼 [LinkedIn](https://linkedin.com/in/navenshia-naidoo) | 💻 [GitHub](https://github.com/navenshia-naidoo)

---

## Summary

Passionate 22-year-old South African Frontend Developer with expertise in React, TypeScript, and modern web technologies. Native English speaker with multilingual abilities (Afrikaans, Spanish, Japanese) and strong problem-solving skills. Experienced in building responsive, accessible web applications and seeking opportunities to contribute to innovative projects.

---

## Technical Skills

### Frontend
- **JavaScript Frameworks:** React, Vue.js
- **Languages:** TypeScript, JavaScript, HTML5, CSS3
- **Styling:** Responsive Design, CSS Grid/Flexbox

### Tools & Development
- **Version Control:** Git, GitHub
- **Build Tools:** Vite, npm
- **APIs:** REST API Integration, JWT Authentication
- **Best Practices:** Accessibility (ARIA), Cross-browser compatibility

---

## Projects

### Password Validation Challenge
**React • TypeScript • API Integration** | *2025*

- Built comprehensive form validation with real-time password criteria checking
- Implemented secure API integration with JWT authentication
- Added full accessibility support with ARIA attributes and screen reader compatibility
- Handled multiple error scenarios with user-friendly messaging
- **Demo:** Successfully integrated with HENNGE admission challenge API

### Portfolio Website
**Vue.js • CSS3 • Responsive Design** | *2024*

- Designed and developed responsive portfolio showcasing technical projects
- Implemented smooth animations and modern UI/UX principles
- Optimized for performance and cross-browser compatibility

### [Add Your Other Projects Here]
**Tech Stack** | *Year*

- Achievement or feature you built
- Technical challenge you solved
- Impact or result you achieved

---

## Experience

### Frontend Developer (Freelance/Personal Projects)
*2023 - Present*

- Developed multiple web applications using React and Vue.js
- Implemented responsive designs with focus on user experience
- Integrated with various APIs and handled authentication flows
- Applied accessibility best practices and modern development workflows

### [Previous Role if Applicable]
**Company Name** | *Dates*

- Bullet point describing your achievements
- Quantified results where possible
- Technologies used and problems solved

---

## Languages

- **English:** Native (Home Language)
- **Afrikaans:** Fluent
- **Spanish:** Conversational
- **Japanese:** Basic (Learning - conversational level)

---

## Education

### Computer Science / Web Development
**Self-Taught & Online Courses** | *2023 - Present*

- Completed comprehensive courses in React, TypeScript, and modern web development
- Built multiple projects demonstrating frontend development skills
- Continuously learning new technologies and best practices

### [Your Degree if Applicable]
**University Name** | *Year*

- Relevant coursework or achievements
- GPA if strong (3.5+ typically)

---

## Additional Information

- **Availability:** Open to full-time opportunities in Japan
- **Work Authorization:** [Your visa status/work eligibility]
- **Interests:** Technology, web development, Japanese culture
- **GitHub:** Active contributor with multiple public repositories

---

*Last updated: July 2025*
